<script setup lang="ts">
import { getObjectType, getSubjectList, getTaskList } from '@/api/taskCenter'
import { useSettingStore } from '@/stores/modules/setting'
import BScroll from '@better-scroll/core'
import TaskItem from './TaskItem.vue'

const props = defineProps({
  subject: {
    type: [String, Number],
    default: '',
  },
  type: {
    type: [String, Number],
    default: -1,
  },
  moduleItem: {
    type: Object,
    default: () => {},
  },
})
const emit = defineEmits(['ruleChange'])
let { menuCollapsed } = toRefs(useSettingStore())
const isCreate = defineModel<boolean>('isCreate')
let subjectList: any = $ref([])
let currentSubject: any = $ref(1)
let bs: any = $ref(null)
const card = [
  {
    title: 'AI课任务',
    className: 'aiTask',
    routerName: 'AiTaskList',
    // isHidden: true,
  },
  {
    title: '学科网选题',
    className: 'xkw',
    query: { pageType: 1 },
    routerName: 'CreateTask',
  },
  {
    title: '校本练习题',
    className: 'xblxt',
    query: { pageType: 2 },
    routerName: 'CreateTask',
  },
  {
    title: '资源',
    className: 'resource',
    query: { pageType: 3 },
    routerName: 'CreateTask',
  },
  {
    title: '错题任务',
    className: 'errorTask',
    routerName: 'ErrorTask',
  },
  {
    title: '综合任务',
    className: 'zhrw',
    routerName: 'ComprehensiveTask',
    isHidden: true,
  },

]
let objectTypeList: any = $ref([])
let currentType: any = $ref(-1)
let taskList: any = $ref([])
let loading = $ref(true)
const scrollRef: any = $ref(null)
let leftBarWidth = $ref('')

async function getSubjectListApi() {
  try {
    const res = await getSubjectList()
    subjectList = res
    if (!res.length) return
    currentSubject = currentSubject || res[0].sysSubjectId
  }
  catch (error) {
    loading = false
    console.log('⚡[ error ] >', error)
  }
}

async function getObjectTypeApi() {
  const res = await getObjectType()
  objectTypeList = res
  objectTypeList.unshift({
    id: -1,
    title: '全部',
  })
  currentType = currentType || -1
}

function toList() {
  emit('ruleChange', {
    currentSubject,
    currentType,
  })
  isCreate.value = false
}

const router = useRouter()

function toCreate(item) {
  let subject = subjectList.find(item => item.sysSubjectId == currentSubject)
  let query = {
    ...item.query,
    subjectId: currentSubject,
    subjectName: subject?.sysSubjectName,
  }
  if (item.routerName == 'ComprehensiveTask') query = {}
  router.push({
    name: item.routerName,
    query,
  })
}

async function getListApi(option: any = {}) {
  try {
    if (!subjectList.length) return
    if (!option?.id)
      loading = true

    const obj = {
      classType: -1,
      taskType: -1,
      arrangeObjectType: currentType,
      arrangeTime: -1,
      schoolClassId: -1,
      sysSubjectId: currentSubject,
      createTimeSort: 'DESC',
      page: 1,
      pageSize: $g.isPC ? 12 : 9,
    }
    const res = await getTaskList(obj)
    if (!res?.list?.length) return taskList = []
    taskList = res.list
  }
  catch (error) {
    console.log('⚡[ error ] >', error)
  }
  finally {
    loading = false
  }
}

function leftBarChange(time = 300) {
  if (time) {
    setTimeout(() => {
      refresh(time)
    }, time)
    return
  }
  refresh(time)
}

function refresh(time) {
  const newTime = time ? 500 : 200
  let initWidth = $g.isPC ? 180 : 0
  let width = menuCollapsed.value ? 0 : initWidth
  width = width + 42
  leftBarWidth = `calc(100vw - ${width}px)`
  setTimeout(() => {
    bs?.refresh()
  }, newTime)
}

function scrollSet() {
  nextTick(() => {
    bs = new BScroll(scrollRef, {
      scrollX: true,
      scrollY: false,
      click: true,
    })
    leftBarChange(0)
  })
}

onBeforeMount(async () => {
  currentSubject = props.subject
  currentType = props.type
  getObjectTypeApi()
  await getSubjectListApi()
  getListApi()
  scrollSet()
})

onMounted(() => {
  const cardItem: any = card.find(item => item.title == '综合任务')
  cardItem.isHidden = !props.moduleItem?.isOpen || props.moduleItem?.isOpen == 1
})

watch(() => props.moduleItem, (val) => {
  const cardItem: any = card.find(item => item.title == '综合任务')
  cardItem.isHidden = !val?.isOpen || val?.isOpen == 1
})

onBeforeUnmount(() => {
  bs.destroy()
  bs = null
})

const noRefresh = $ref(['/teacher/taskCenter/report/resourceReport', '/teacher/groupManage'])

onActivated(() => {
  let forwardUrl = window.history.state?.forward?.split('?')[0]
  if (!noRefresh.includes(forwardUrl) || !forwardUrl)
    getListApi()
})

watch(() => menuCollapsed.value, (val) => {
  leftBarChange(val ? 300 : 0)
})
</script>

<template>
  <div ref="contentRef" class="w-full">
    <div class="flex h-32px">
      <span class="text-19px font-600">布置学生任务</span>
      <template v-if="subjectList.length">
        <div
          v-if="subjectList.length == 1"
          class="text-13px ml-21px pt-6px text-[#74788D]"
        >
          {{ subjectList[0].sysSubjectName }}
        </div>
        <div v-else class="ml-21px text-13px flex items-center">
          <el-select
            v-model="currentSubject"
            style="min-width: 60px"
            @change="getListApi"
          >
            <el-option
              v-for="item in subjectList"
              :key="item.sysSubjectId"
              :label="item.sysSubjectName"
              :value="item.sysSubjectId"
            />
            <template #label="{ label }">
              <div class="w-fit flex items-center">
                <div class="text-[#74788D]">
                  {{ label }}
                </div>
                <div class="w-15px">
                  <img
                    :src="$g.tool.getFileUrl('taskCenter/select-down.png')"
                    class="w-15px h-15px"
                  />
                </div>
              </div>
            </template>
          </el-select>
        </div>
      </template>
    </div>

    <div class="flex justify-between items-center text-[#3E3E3E] mt-13px mb-26px w-full overflow-hidden">
      <div
        ref="scrollRef"
        class="wrapper"
        :style="{ width: leftBarWidth }"
      >
        <div class="w-fit overflow-auto">
          <div
            v-for="item in card"
            v-show="!item.isHidden"
            :key="item.title"
            :class="[item.className, $g.isPC ? 'hover:opacity-70' : '']"
            class="w-203px h-101px mr-13px py-13px px-16px van-haptics-feedback flex-shrink-0 inline-block"
            @click="toCreate(item)"
          >
            <div class="text-17px font-500">
              {{ item.title }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <span class="mr-11px text-[#636772] text-17px font-600">近期任务</span>
        <g-radio
          v-model="currentType"
          :option="objectTypeList"
          :replace-keys="{ id: 'id', name: 'title' }"
          item-class="border border-[#E6E6E6] bg-white px-15px py-5px"
          active-item-class="!border-[#646AB4] !bg-[#ECEFFF] !text-[#5864F8]"
          @change="getListApi"
        />
      </div>
      <div class="cursor-pointer flex items-center text-13px" @click="toList">
        <span>查看更多</span>
        <img
          :src="$g.tool.getFileUrl('taskCenter/more.png')"
          class="w-12px h-12px"
          alt="more"
        />
      </div>
    </div>
    <g-loading v-if="loading" class="h-200px"></g-loading>
    <template v-else>
      <div
        v-if="taskList.length"
        class="w-full grid gap-x-[19px] gap-y-[16px] justify-between mt-18px"
        :class="!$g.isPC ? 'grid-cols-3' : 'grid-cols-4'"
      >
        <TaskItem
          v-for="item in taskList"
          :key="item.taskId"
          :data="item"
          @get-list-api="getListApi"
        ></TaskItem>
      </div>
      <g-empty v-else></g-empty>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.wrapper{
  white-space: nowrap;
  overflow: hidden;
}

.xkw {
  background: url("@/assets/img/taskCenter/xkw.png") center / contain no-repeat;
}

.xblxt {
  background: url("@/assets/img/taskCenter/xblxt.png") center / contain no-repeat;
}

.resource {
  background: url("@/assets/img/taskCenter/resource.png") center / contain
    no-repeat;
}

.errorTask {
  background: url("@/assets/img/taskCenter/errorTask.png") center / contain
    no-repeat;
}

.zhrw{
  background: url("@/assets/img/taskCenter/zhrw.png") center / contain
    no-repeat;
}
.aiTask{
  background: url("@/assets/img/taskCenter/aiTask.png") center / contain
    no-repeat;
}
</style>

<style lang="scss">
.driver-button-class {
  .driver-popover-next-btn {
    padding: 6px 16px;
    border: none;
    color: white;
    font-size: 14px;
    background-color: #6275f6 !important;
    text-shadow: none;
  }
}
</style>
