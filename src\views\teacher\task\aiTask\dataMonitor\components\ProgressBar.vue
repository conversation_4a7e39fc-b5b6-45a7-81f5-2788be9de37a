<script setup lang="ts">
import { getTaskStudentLearnList, remindCorrectApi } from '@/api/aiTask'

/** 课程数据接口 */
interface IBookCatalog {
  /** 书目录ID */
  bookCatalogId: number
  /** 目录名称 */
  bookCatalogName: string
  /** 学习时长 */
  learnDuration: number
  /** 已解决的举手次数 */
  raiseHandSolvedCount: number
  /** AI未解决的举手次数 */
  raiseAiHandUnSolvedCount: number
  /** 老师未解决的举手次数 */
  raiseTeacherHandUnSolvedCount: number
  /** 任务是否完成:1-否,2-是,3-进行中 */
  isFinish: number
}

/** API返回的学生数据接口 */
interface IStudentData {
  /** 学生ID */
  schoolStudentId: number
  /** 学生姓名 */
  schoolStudentName: string
  /** 学生头像 */
  headPicture: string
  /** 错误数量 */
  errorNum: number
  /** 已完成目录列表 */
  bookCatalogList: IBookCatalog[]
}

/** 组件内部使用的学生数据结构 */
interface IStudent {
  /** 学生姓名 */
  name: string
  /** 当前步骤 */
  currentStep: number
  /** 每个步骤的举手信息 */
  stepHandData: {
    /** 请教老师次数 */
    teacherHandCount: number
    /** 请教AI次数 */
    aiHandCount: number
    /** 老师已解答次数 */
    answeredCount: number
  }[]
  /** 学生ID */
  studentId: number
  /** 学生头像 */
  headPicture: string
}

const props = defineProps<{
  taskId: number
  isTaskCompleted?: boolean
  isTaskStarted?: boolean
}>()

const emit = defineEmits<{
  (e: 'fullscreenChange', value: boolean): void
  (e: 'refresh'): void
}>()

// 定义课程步骤数组，从API获取
let arr = $ref<string[]>([])

/**
 * 处理文字省略显示
 * @param text 原始文本
 * @param maxUnits 最大显示单位数（中文=2单位，英文=1单位，默认12单位）
 * @returns 处理后的文本
 */
function truncateText(text: string, maxUnits = 12): string {
  if (!text) return ''

  let totalUnits = 0
  let result = ''

  for (let i = 0; i < text.length; i++) {
    const char = text[i]
    // 判断是否为中文字符（包括中文标点符号）
    const isChinese = /[\u4E00-\u9FA5\u3000-\u303F\uFF00-\uFFEF]/.test(char)
    const charUnits = isChinese ? 2 : 1

    if (totalUnits + charUnits > maxUnits) {
      // 如果添加当前字符会超出限制，则停止并添加省略号
      result += '...'
      break
    }

    result += char
    totalUnits += charUnits
  }

  return result
}
let isFullScreen = $ref(false)
// 一键提醒订正按钮loading状态
let isRemindLoading = $ref(false)
// 学生数据加载状态
let isLoadingStudents = $ref(false)
// 轮询定时器ID
let pollingTimer = $ref<number | null>(null)
// 轮询间隔(毫秒)
const POLLING_INTERVAL = 30000 // 30秒

const fullscreenIcon = computed(() => {
  return isFullScreen
    ? $g.tool.getFileUrl('dataMonitor/exitFullScreen.png')
    : $g.tool.getFileUrl('dataMonitor/fullscreen.png')
})

/**
 * 切换全屏状态 - 容器内全屏
 */
function toggleFullScreen() {
  isFullScreen = !isFullScreen
  // 向父组件发送全屏状态变化事件
  emit('fullscreenChange', isFullScreen)
}

// 学生数据
let students = $ref<IStudent[]>([])

function remindCorrect() {
  isRemindLoading = true
  remindCorrectApi({
    taskId: props.taskId,
  }).then((res) => {
    $g.showToast('一键提醒订正成功')
  }).finally(() => {
      isRemindLoading = false
    })
}

function refresh() {
  // 向父组件发送刷新事件
  emit('refresh')
  // 刷新学生进度数据，显示loading
  getStudentProgressData(true)
}

/**
 * 开始轮询数据
 */
function startPolling() {
  // 如果任务已结束或未开始，不启动轮询
  if (props.isTaskCompleted || props.isTaskStarted === false) {
    console.log('任务未开始或已结束，不启动轮询')
    return
  }

  stopPolling() // 先清除可能存在的定时器

  // 创建新的定时器，每30秒轮询一次
  pollingTimer = setInterval(() => {
    console.log('轮询更新数据...')
    // 轮询时不显示loading
    getStudentProgressData(false)
  }, POLLING_INTERVAL) as unknown as number
}

/**
 * 停止轮询
 */
function stopPolling() {
  if (pollingTimer !== null) {
    clearInterval(pollingTimer)
    pollingTimer = null
  }
}

/**
 * 获取学生进度数据
 * @param showLoading 是否显示加载状态，默认为true
 */
function getStudentProgressData(showLoading = true) {
  // 只有在需要显示loading时才设置加载状态
  if (showLoading)
    isLoadingStudents = true

  getTaskStudentLearnList({
    taskId: props.taskId,
  }).then((res) => {
    // 从第一个学生数据中提取课程列表构建步骤显示
    if (res.length > 0 && res[0].bookCatalogList && res[0].bookCatalogList.length > 0)
      arr = res[0].bookCatalogList.map(item => item.bookCatalogName)

    // 将API返回的学生数据映射为组件需要的格式
    students = res.map((item: IStudentData) => {
      // 从课程列表中获取当前步骤
      let currentStep = -1 // 初始值设为-1，表示没有进行中的课程

      // 为每个步骤构建举手信息数组
      const stepHandData: {
        teacherHandCount: number
        aiHandCount: number
        answeredCount: number
      }[] = []

      // 如果有课程列表数据
      if (item.bookCatalogList && item.bookCatalogList.length > 0) {
        // 遍历所有课程，构建每个步骤的举手数据
        for (let i = 0; i < item.bookCatalogList.length; i++) {
          const course = item.bookCatalogList[i]

          // 为每个步骤保存举手数据
          stepHandData.push({
            teacherHandCount: course.raiseTeacherHandUnSolvedCount,
            aiHandCount: course.raiseAiHandUnSolvedCount,
            answeredCount: course.raiseHandSolvedCount,
          })

          // 更新当前步骤
          if (course.isFinish === 2) {
            // 已完成的课程，继续寻找下一个
            currentStep = i + 1 // 下一个步骤为当前进行的步骤
          }
          else if (course.isFinish === 3) {
            // 进行中的课程
            currentStep = i
          }
        }
      }

      return {
        name: item.schoolStudentName || '未知学生',
        currentStep,
        stepHandData,
        studentId: item.schoolStudentId,
        headPicture: item.headPicture,
      }
    })
  }).catch((err) => {
      console.error('获取学生进度数据失败:', err)
      // 发生错误时清空数据
      students = []
      arr = []
    }).finally(() => {
    // 只有在显示loading时才需要关闭loading
      if (showLoading)
        isLoadingStudents = false
    })
}

// 监听任务状态变化
watch(
  () => [props.isTaskCompleted, props.isTaskStarted],
  ([isCompleted, isStarted]) => {
    if (isCompleted || isStarted === false) {
      // 任务已结束或未开始，停止轮询
      stopPolling()
    }
    else {
      // 任务进行中，启动轮询
      startPolling()
    }
  },
)

// 组件卸载时停止轮询
onUnmounted(() => {
  stopPolling()
})

// 滚动同步逻辑
let courseHeaderScrollRef = $ref<HTMLElement>()
let studentsScrollRef = $ref<HTMLElement>()

/**
 * 同步左右滚动
 */
function syncHorizontalScroll(event: Event) {
  const target = event.target as HTMLElement
  const scrollLeft = target.scrollLeft

  // 同步课程标题栏的滚动
  if (courseHeaderScrollRef && courseHeaderScrollRef !== target)
    courseHeaderScrollRef.scrollLeft = scrollLeft

  // 同步学生数据容器的滚动
  if (studentsScrollRef && studentsScrollRef !== target)
    studentsScrollRef.scrollLeft = scrollLeft
}

onMounted(() => {
  // 首次加载显示loading
  getStudentProgressData(true)
  // 启动轮询，但需要考虑任务状态
  startPolling()

  // 添加滚动事件监听
  nextTick(() => {
    if (courseHeaderScrollRef)
      courseHeaderScrollRef.addEventListener('scroll', syncHorizontalScroll)

    if (studentsScrollRef)
      studentsScrollRef.addEventListener('scroll', syncHorizontalScroll)
  })
})

// 组件卸载时停止轮询和移除事件监听
onUnmounted(() => {
  stopPolling()

  // 移除滚动事件监听
  if (courseHeaderScrollRef)
    courseHeaderScrollRef.removeEventListener('scroll', syncHorizontalScroll)

  if (studentsScrollRef)
    studentsScrollRef.removeEventListener('scroll', syncHorizontalScroll)
})
</script>

<template>
  <div class="progress-container">
    <!-- 只有在有学生数据时才显示头部区域和步骤 -->
    <template v-if="students.length > 0 || isLoadingStudents">
      <div class="header-section">
        <div class="flex justify-between mb-17px">
          <div class="title text-15px font-600 flex-cc">
            学生实时进度栏
            <svg-ri-loop-right-fill class="ml-10px cursor-pointer" @click="refresh" />
          </div>
          <div class="flex text-13px items-center select-none">
            <img
              class="w-17px h-17px cursor-pointer"
              src="@/assets/img/dataMonitor/js-laoshi.png"
              alt=""
            >
            <div class="mr-17px">
              请教老师
            </div>
            <img
              class="w-17px h-17px cursor-pointer"
              src="@/assets/img/dataMonitor/js-AI.png"
              alt=""
            >
            <div class="mr-17px">
              请教AI老师
            </div>
            <img
              class="w-17px h-17px cursor-pointer"
              src="@/assets/img/dataMonitor/js-laoshi2.png"
              alt=""
            >
            <div class="">
              老师已解答
            </div>
            <img
              v-if="!$g.isFlutter"
              class="w-17px h-17px cursor-pointer ml-34px"
              :src="fullscreenIcon"
              :alt="isFullScreen ? '退出全屏' : '全屏'"
              @click="toggleFullScreen"
            >
          </div>
        </div>
      </div>
    </template>

    <!-- body -->
    <div class="content-wrapper">
      <!-- 课程标题栏 - 固定在顶部但支持左右滚动 -->
      <div v-if="arr.length > 0" class="course-header-fixed">
        <div ref="courseHeaderScrollRef" class="course-header-scroll">
          <div class="flex">
            <!-- 顶部课程 -->
            <div class="mr-4px bg-[#F1F1F1] flex-cc rounded-[4px] flex-shrink-0 w-60px">
              step
            </div>
            <div
              v-for="(item, index) in arr"
              :key="index"
              class="item"
              :class="{
                'first-item': index === 0,
                'last-item': index === arr.length - 1,
              }"
            >
              {{ truncateText(item) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 学生数据容器 - 可上下滚动，与课程标题栏左右滚动同步 -->
      <div ref="studentsScrollRef" class="students-scroll-container">
        <!-- 加载状态显示 -->
        <div v-if="isLoadingStudents" class="flex-cc py-50px">
          <g-loading class="h-200px" />
        </div>
        <!-- 学生数据显示 -->
        <template v-else-if="students.length > 0">
          <template v-for="(student, index) in students" :key="index">
            <div class="student-row">
              <div class="student-info flex items-center">
                <div class="flex-cc flex-col w-60px">
                  <!-- <el-avatar
                    class="w-30px h-30px"
                    :src="student.headPicture || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'"
                  >
                    <template #default></template>
    </el-avatar> -->
                  <div class="student-name">
                    {{ student.name }}
                  </div>
                </div>

                <!-- 学生进度条 -->
                <div class="flex flex-1 progress-items-container">
                  <template v-for="(item, stepIndex) in arr" :key="stepIndex">
                    <!-- 进度条项目 -->
                    <div
                      class="progress-item"
                      :class="{
                        'active': stepIndex < student.currentStep,
                        'in-progress': stepIndex === student.currentStep,
                        'first-progress-item': stepIndex === 0,
                        'last-progress-item': stepIndex === arr.length - 1,
                      }"
                    >
                      <div
                        v-if="student.stepHandData[stepIndex] && (student.stepHandData[stepIndex].teacherHandCount > 0 || student.stepHandData[stepIndex].aiHandCount > 0 || student.stepHandData[stepIndex].answeredCount > 0)"
                        class="status-icons"
                      >
                        <!-- 循环显示请教老师图标 -->
                        <div class="icon-group teacher-icons ml-15px">
                          <template v-for="n in student.stepHandData[stepIndex].teacherHandCount" :key="`teacher-${n}`">
                            <img
                              class="status-icon"
                              :style="{
                                marginLeft: n > 1 ? '-16px' : '0',
                                zIndex: student.stepHandData[stepIndex].teacherHandCount - n + 1,
                              }"
                              src="@/assets/img/dataMonitor/js-laoshi.png"
                              alt=""
                            >
                          </template>
                        </div>

                        <!-- 循环显示请教AI图标 -->
                        <div class="icon-group ai-icons ml-3px">
                          <template v-for="n in student.stepHandData[stepIndex].aiHandCount" :key="`ai-${n}`">
                            <img
                              class="status-icon"
                              :style="{
                                marginLeft: n > 1 ? '-16px' : '0',
                                zIndex: student.stepHandData[stepIndex].aiHandCount - n + 1,
                              }"
                              src="@/assets/img/dataMonitor/js-AI.png"
                              alt=""
                            >
                          </template>
                        </div>

                        <!-- 循环显示已解答图标 -->
                        <div class="icon-group answered-icons ml-3px">
                          <template v-for="n in student.stepHandData[stepIndex].answeredCount" :key="`answered-${n}`">
                            <img
                              class="status-icon"
                              :style="{
                                marginLeft: n > 1 ? '-16px' : '0',
                                zIndex: student.stepHandData[stepIndex].answeredCount - n + 1,
                              }"
                              src="@/assets/img/dataMonitor/js-laoshi2.png"
                              alt=""
                            >
                          </template>
                        </div>
                      </div>
                    </div>

                    <!-- 步骤圆点，包括最后一个步骤也显示 -->
                    <div
                      class="step-circle"
                      :class="{
                        'in-progress': stepIndex === student.currentStep,
                        'uncompleted': stepIndex > student.currentStep,
                        'connected-step': index !== students.length - 1,
                      }"
                      :data-step-index="stepIndex"
                    >
                      <span>{{ stepIndex + 1 }}</span>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </template>
        </template>
        <!-- 没有学生数据时显示空状态 -->
        <div v-else class="flex-cc py-100px">
          <g-empty description="暂无学生数据" />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.progress-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  /* 填满父容器高度 */
  padding: 17px;
  padding-right: 0;
  overflow: hidden;
  /* 防止整体出现滚动条 */
}

.header-section {
  flex-shrink: 0;
  padding-right: 17px;
}

.content-wrapper {
  display: flex;
  flex: 1;
  flex-direction: column;
  min-height: 0; /* 确保子元素可以收缩 */
  overflow: hidden;
}

.course-header-fixed {
  flex-shrink: 0;
  background: white;
  padding-right: 17px;
}

.course-header-scroll {
  overflow: auto hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  /* 确保滚动容器内容不换行 */
  .flex {
    min-width: max-content;
    padding-bottom: 10px;
  }
}

.students-scroll-container {
  flex: 1;
  min-width: 500px;
  overflow: auto;
  padding-right: 17px;
  padding-bottom: 0;

  /* 确保内容不换行以支持水平滚动 */
  .student-row {
    min-width: max-content;
  }
}

.student-row {
  margin-bottom: 15px;
}

.student-info {
  display: flex;
  width: 100%;
}

.student-name {
  font-size: 12px;
}

.flex {
  display: flex;
}

.progress-items-container {
  position: relative;
  display: flex;
  align-items: center;
  width: calc(100% - 64px);
  margin-left: 4px;
}

.progress-item {
  position: relative;
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
  height: 25px;
  border-radius: 11px;
  background-color: #F3F4F9;
  min-width: 119px;

  &.active {
    // border: 1px solid #99F;
    background: linear-gradient(90deg, #B2B2FF 0%, #D8D8FF 100%), #F3F4F9;
  }

  &.in-progress {
    // border: 1px solid #99F;
    background: linear-gradient(to right,
        #B2B2FF 0%,
        #D8D8FF 20%,
        #F3F4F9 20%,
        #F3F4F9 100%);
    border-right: none;
  }

  &.first-progress-item {
    border-radius: 11px 0 0 11px;
  }

  &.last-progress-item {
    border-radius: 0 11px 11px 0;
  }
}

.step-circle {
  flex-shrink: 0;
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  margin: 0 -12px;
  border: 2px solid white;
  border-radius: 50%;
  background: #7E80FF;
  color: #666;
  color: white;
  font-size: 12px;
  box-shadow: 0 1px 4px rgb(0 0 0 / 10%);

  &.uncompleted,
  &.in-progress {
    background-color: white;
    border: 1px solid #615BE3;
    outline: 2px solid white;
    color: #615BE3;
  }

  &.connected-step::after {
    position: absolute;
    top: 26px;
    left: 50%;
    z-index: -10;
    height: 50px;
    content: "";
    /* 根据实际间距调整 */
    border-left: 1.5px dashed #E8E8E8;
    transform: translateX(-50%);

  }
}

.status-icons {
  position: absolute;
  right: 5px;
  bottom: -1px;
  left: 5px;
  display: flex;
  justify-content: flex-start;
  width: calc(100% - 10px);
  height: 25px;
  flex-wrap: wrap;
}

.icon-group {
  display: flex;
  align-items: center;
}

.status-icon {
  position: relative;
  z-index: 1;
  width: 23px;
  height: 23px;
}

.item {
  position: relative;
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
  height: 30px;
  padding: 0 20px;
  border-radius: 4px 0 0 4px;
  background: #F1F1F1;
  font-size: 15px;
  margin-right: 4px;
  white-space: nowrap;
  overflow: visible;

  // flex-shrink: 0;
  // width: 180px;
  min-width: 120px;

  &::before {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 0;
    height: 0;
    content: '';
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
    border-left: 12px solid #fff;
  }

  &::after {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 3;
    width: 0;
    height: 0;
    content: '';
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
    border-left: 12px solid #F1F1F1;
    transform: translateX(100%);
  }
}

.first-item::before {
  display: none;
}

.last-item::after {
  display: none;
}

.last-item {
  margin-right: 0;
  border-radius: 0 4px 4px 0;
}
</style>
