<script setup lang="ts">
import { ref } from 'vue'

// 计数器状态
const count = ref<number>(0)

// 增加计数
function increment(): void {
  count.value++
}

// 减少计数
function decrement(): void {
  count.value--
}

// 重置计数
function reset(): void {
  count.value = 0
}
</script>

<template>
  <div class="counter-container">
    <div class="counter-display">
      <h2 class="counter-title">
        计数器
      </h2>
      <div class="counter-value">
        {{ count }}
      </div>
    </div>

    <div class="counter-controls">
      <button
        class="counter-btn decrement"
        :disabled="count <= 0"
        @click="decrement"
      >
        -
      </button>

      <button
        class="counter-btn reset"
        @click="reset"
      >
        重置
      </button>

      <button
        class="counter-btn increment"
        @click="increment"
      >
        +
      </button>
    </div>
  </div>
</template>

<style scoped>
.counter-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 20px;
  padding: 40px;
  border-radius: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 400px;
  box-shadow: 0 10px 30px rgb(0 0 0 / 20%);
}

.counter-display {
  text-align: center;
  margin-bottom: 30px;
}

.counter-title {
  color: white;
  font-size: 2rem;
  font-weight: 300;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgb(0 0 0 / 30%);
}

.counter-value {
  padding: 20px 40px;
  border: 1px solid rgb(255 255 255 / 20%);
  border-radius: 15px;
  background: rgb(255 255 255 / 10%);
  color: white;
  font-size: 4rem;
  font-weight: bold;
  min-width: 120px;
  backdrop-filter: blur(10px);
  text-shadow: 0 2px 4px rgb(0 0 0 / 30%);
}

.counter-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.counter-btn {
  width: 60px;
  height: 60px;
  border: none;
  border-radius: 50%;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgb(0 0 0 / 20%);
  backdrop-filter: blur(10px);
}

.counter-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgb(0 0 0 / 30%);
}

.counter-btn:active {
  transform: translateY(0);
}

.increment {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.decrement {
  background: linear-gradient(135deg, #f44336, #da190b);
  color: white;
}

.decrement:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 5px rgb(0 0 0 / 10%);
}

.reset {
  width: 80px;
  border-radius: 25px;
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
  font-size: 1rem;
}
</style>`
