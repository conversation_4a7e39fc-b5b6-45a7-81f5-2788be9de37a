<script setup lang="ts" name="AiTaskList">
import { getAiCourseTaskList, getClassList, getSchoolGradeList } from '@/api/aiTask'
import SearchFrom from './components/SearchForm.vue'

const router = useRouter()
const route = useRoute()
let formOptions: any = $ref({
  items: {
    time: {
      label: '任务时间',
      slot: true,
      option: [{
        id: 'all',
        name: '全部',
      },{
        id: 1,
        name: '今天',
      },{
        id: 2,
        name: '本周',
      },{
        id: 3,
        name: '上周',
      },{
        id: 4,
        name: '近两周',
      }],
    },
    grade: {
      label: '选择年级',
      option: [],
    },
    class: {
      label: '任务班级',
      option: [],
    },
    // state: {
    //   label: '任务状态',
    //   idName: 'id',
    //   labelName: 'title',
    //   option: [{ id: 'all', name: '全部' }, { id: 1, name: '待生效' }, { id: 2, name: '进行中' }, { id: 3, name: '已过期' }],
    // },
    keyword: {
      label: '搜索任务',
      slot: true,
    },
  },
  data: {
    time: 'all',
    state: 'all',
    keyword: '',
    grade: 'all',
    class: 'all',
    date: null,
  },
})
const tableOptions = reactive<any>({
  loading: true,
  ref: null as any,
  column: [
    {
      label: '任务名称',
      prop: 'taskName',
      minWidth: 160,
    },
    // {
    //   label: '任务状态',
    //   prop: 'state',
    //   minWidth: 100,
    //   slot: true,
    // },
    {
      label: '完成人数',
      prop: 'finishCount',
      minWidth: 100,
      slot: true,
    },
    {
      label: '平均进度',
      prop: 'averageProgress',
      minWidth: 100,
      sort: true,
      formatter(row) {
        if (!row?.averageProgress)
          return '/'

        return `${row.averageProgress}%`
      },
    },
    {
      label: '平均学习用时',
      prop: 'avgLearnDuration',
      minWidth: 100,
      sort: true,
      formatter(row) {
        if (!row?.avgLearnDuration)
          return '/'

        // 转换秒为时分秒
        const seconds = Number(row.avgLearnDuration)
        const h = Math.floor(seconds / 3600)
        const m = Math.floor((seconds % 3600) / 60)
        const s = seconds % 60
        const pad = n => n.toString().padStart(2, '0')
        if (h > 0)
          return `${pad(h)}小时${pad(m)}分${pad(s)}秒`

        else if (m > 0)
          return `${pad(m)}分${pad(s)}秒`

        else
          return `${pad(s)}秒`
      },
    },
    {
      label: '平均答题数',
      prop: 'avgAnswerCount',
      minWidth: 100,
      formatter(row) {
        if (!row.avgAnswerCount) return '/'
        return `${row.avgAnswerCount}`
      },
    },
    {
      label: '生效时间',
      prop: 'effectTime',
      minWidth: 100,
      sort: true,
      formatter(row) {
        if (!row?.effectTime)
          return '/'
        return $g.dayjs(row?.effectTime).format('YYYY/MM/DD HH:mm:ss')
      },
    },
    {
      label: '创建人',
      prop: 'userName',
      minWidth: 100,
    },
    {
      label: '操作',
      prop: 'cz',
      slot: true,
      fixed: 'right',
      minWidth: 110,
    },
  ],
  data: [],
  pageOptions: {
    page: 1,
    pageSize: 10,
    total: 0,
  },
})
const STATE_TYPE = {
  1: {
    text: '待生效',
    className: 'text-[#FF7D29]',
  },
  2: {
    text: '生效中',
    className: 'text-[#00B34A]',
  },
  3: {
    text: '已完成',
    className: 'text-[#00B34A]',
  },
  4: {
    text: '已关闭',
    className: 'text-[#999]',
  },
}

function dateChoose() {
  formOptions.data.time = formOptions.data.date == null ? 'all' : null
  tableOptions.pageOptions.page = 1
  getAiCourseTaskListApi()
}
function search() {
  tableOptions.pageOptions.page = 1
  getAiCourseTaskListApi()
}
function getDateRange(type) {
  const today = $g.dayjs()
  let res: any = null
  switch (type) {
    case 'all':
      res = []
      break
    case 1:
      // 今天
      res = [today.startOf('day').format('YYYY-MM-DD HH:mm:ss'), today.endOf('day').format('YYYY-MM-DD HH:mm:ss')]
      break
    case 2:
      // 本周从周一到周天
      res = [
        today.startOf('week').add(1, 'day').format('YYYY-MM-DD 00:00:00'),
today.endOf('week').add(1, 'day').format('YYYY-MM-DD 23:59:59'),
      ]
      break
    case 3: {
      // 上周
      res = [
        today.startOf('week').subtract(1, 'week').add(1, 'day').format('YYYY-MM-DD 00:00:00'),
today.endOf('week').subtract(1, 'week').add(1, 'day').format('YYYY-MM-DD 23:59:59'),
      ]
      break
    }
    case 4: {
      // 近两周（包括本周和本周前的完整7天，共14天）
      const startOfLastWeek = today.startOf('week').subtract(1, 'week').add(1, 'day').startOf('day')
      const endOfThisWeek = today.endOf('week').add(1, 'day').endOf('day')
      res = [startOfLastWeek.format('YYYY-MM-DD HH:mm:ss'), endOfThisWeek.format('YYYY-MM-DD HH:mm:ss')]
      break
    }
    default:
      res = null
      break
  }
  formOptions.data.date = res
  tableOptions.pageOptions.page = 1
  getAiCourseTaskListApi()
}

function sortChange({
  order,
  prop,
}) {
  const orderField = order && prop ? prop : ''
  const orderAsc = order === 'asc' ? 1 : order === 'desc' ? 2 : ''
  getAiCourseTaskListApi(orderField, orderAsc)
}
function createTask() {
  router.push({
    name: 'CreateAiTask',
    query: route.query,
  })
}
async function getAiCourseTaskListApi(orderField = '', orderAsc: any = '') {
  try {
    tableOptions.loading = true
    let res = await getAiCourseTaskList({
      startTime: formOptions.data.date?.length ? formOptions.data.date[0] : null,
      endTime: formOptions.data.date?.length ? formOptions.data.date[1] : null,
      taskClassId: formOptions.data.class == 'all' ? '' : formOptions.data.class,
      taskName: formOptions.data.keyword,
      page: tableOptions.pageOptions.page,
      pageSize: tableOptions.pageOptions.pageSize,
      orderField,
      orderAsc,
      subjectId: route.query?.subjectId ?? '',
    })
    tableOptions.data = res?.list ?? []
    tableOptions.pageOptions.total = res?.total ?? 0
    tableOptions.loading = false
  }
  catch (error) {
    tableOptions.loading = false
    console.log(error)
  }
}
// 年级数据
async function getTimetableGradeListApi() {
  let res = await getSchoolGradeList()
  let tempList = res.map(it => ({
    ...it,
    id: it.sysGradeId,
    name: it.sysGradeName,
  }))
  formOptions.items.grade.option = [{
    id: 'all',
    name: '全部',
  },...tempList]
}
// 班级数据
async function getClassListApi() {
  try {
    let res = await getClassList({ sysGradeId: formOptions.data.grade == 'all' ? '' : formOptions.data.grade })
    let tempList = res.map(it => ({
      id: it.schoolClassId,
      name: it.className,
    }))
    formOptions.items.class.option = [{
      id: 'all',
      name: '全部',
    },...tempList]
  }
  catch (error) {
    formOptions.items.class.option = [{
      id: 'all',
      name: '全部',
    }]
    console.log(error)
  }
}
function goReportPage(row) {
  router.push({
    name: 'AiTaskStudentReport',
    query: {
      taskId: row.taskId,
      title: row.parentBookCatalogName,
    },
  })
}
watch(() => formOptions.data.grade, () => {
  getClassListApi()
})
onMounted(async () => {
  await getTimetableGradeListApi()
  getClassListApi()
  getAiCourseTaskListApi()
})
</script>

<template>
  <div class="p-26px" style="width: 100vw;">
    <g-navbar title="AI课任务"></g-navbar>

    <SearchFrom
      class="bg-[#fff] px-[17px] py-[21px] rounded-[6px] mt-26px"
      :form-option="formOptions"
      @change="getAiCourseTaskListApi"
    >
      <template #time>
        <g-radio
          v-model="formOptions.data.time"
          :option="formOptions.items.time.option"
          item-class="px-10px py-7px text-15px text-[#333] !font-400"
          active-item-class="bg-[#ECEFFF] text-[#5864F8]"
          @change="getDateRange"
        />

        <div class="w-[400px] h-25px ml-10px">
          <el-date-picker
            ref="pickerRef"
            v-model="formOptions.data.date"
            class="-mt-[3px] h-30px w-full"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="dateChoose"
          />
        </div>
      </template>
      <template #keyword>
        <el-input
          v-model="formOptions.data.keyword"
          style="width: 181px"
          class="h-34px"
          clearable
          placeholder="请输入任务名称"
          @keyup.enter="search"
          @clear="search"
        />
        <el-button
          color="#6474FD"
          class="w-64px ml-6px h-[34px] border-none"
          @click="search"
        >
          搜索
        </el-button>
      </template>
    </SearchFrom>
    <div class="mt-17px br-[6px] p-17px bg-[#fff]">
      <div class="flex justify-end items-center">
        <button class="!bg-[#fff] px-13px br-[17px] border border-[#6474FD] text-[#6474FD] h-34px leading-[34px] flex items-center text-13px" @click="createTask">
          <img
            :src="$g.tool.getFileUrl('comprehensiveTask/add.png')"
            class="w-17px h-17px mr-4px"
            alt="add"
          >
          <span>创建任务</span>
        </button>
      </div>
      <g-table
        :border="false"
        :header-cell-style="{
          background: '#6474FD1A',
          color: '#6C6C74',
          fontWeight: '400',
          fontSize: '13px',
        }"
        :cell-style="{
          color: '#333333',
          fontWeight: '400',
          fontSize: '13px',
        }"
        :table-options="tableOptions"
        :highlight-current-row="false"
        @change-page="getAiCourseTaskListApi"
        @sort-change="sortChange"
      >
        <!-- 任务进度 -->
        <template #finishCount="{ row }">
          <div class="flex">
            <el-progress
              :percentage="row.totalCount ? Math.round((row.finishCount / row.totalCount) * 100) : 0"
              :show-text="false"
              :stroke-width="9"
              class="flex-1 mr-4px"
            />
            <span class="text-13px">{{ row.finishCount }}/{{ row.totalCount }}</span>
          </div>
        </template>

        <!-- 任务状态 -->
        <template #state="{ row }">
          <span :class="STATE_TYPE[row.state]?.className">{{ STATE_TYPE[row.state]?.text }}</span>
        </template>
        <template #cz="{ row }">
          <el-button
            type="primary"
            class="p-0"
            text
            @click="goReportPage(row)"
          >
            查看
          </el-button>
          <!-- <el-dropdown trigger="click" placement="top">
            <el-button
              type="primary"
              class="!outline-none p-0 ml-12px"
              text
            >
              操作
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="updateTask">
                  编辑
                </el-dropdown-item>
                <el-dropdown-item command="checkTask">
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown> -->
        </template>
      </g-table>
    </div>
  </div>
</template>

<style scoped>

</style>
