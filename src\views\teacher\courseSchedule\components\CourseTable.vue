<script setup lang="ts">
import { deleteScheduleTaskApi, getScheduleDataApi } from '@/api/courseSchedule'
import AddTypeDialog from './AddTypeDialog.vue'
import MemoDetailDialog from './MemoDetailDialog.vue'

let props = defineProps({
  currentWeek: {
    type: Number,
    default: () => null,
  },
})
let headerList = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
let showAdd = $ref<any>(false)
let currentApiParam = $ref<any>({})
let showMemoDetail = $ref<any>(false)
let loading = $ref<any>(false)
const router = useRouter()
async function getScheduleList() {
  loading = true
  const res = await getScheduleDataApi({ weekIndex: props.currentWeek })
  loading = false
  trList = res || []
}
let trList = $ref<any>([])
function onCreate({
  timetableDate,
  timetableTimeIndex,
  timetableTimeType,
  sysSubjectId,
  beginTime,
  endTime,
  sysSubjectName,
  schoolClassId,
  task,
  existMemo,
}) {
  if ((existMemo && task?.taskName) || (!sysSubjectId && existMemo))
    return

  currentApiParam = {
    timetableDate,
    timetableTimeIndex,
    timetableTimeType,
    sysSubjectId,
    beginTime,
    endTime,
    sysSubjectName,
    task,
    schoolClassId,
    existMemo,
  }

  showAdd = true
}
function onTaskClick({
  taskId,
  taskName,
}) {
  router.push({
    name: 'AiTaskStudentReport',
    query: {
      taskId,
      title: taskName,
    },
  })
}
async function onMemoClick({
  timetableTimeIndex,
  timetableTimeType,
  timetableDate,
}) {
  currentApiParam = {
    timetableDate,
    timetableTimeIndex,
    timetableTimeType,
  }
  showMemoDetail = true
}
function onDeleteTask({
  taskId,
  taskName,
}) {
  $g.confirm({
    content: `确定删除${taskName}吗？`,
  })
    .then(async () => {
      await deleteScheduleTaskApi({
        taskId,
      })
      getScheduleList()
    })
    .catch(() => {})
}
provide('refreshTable', getScheduleList)
// 计算需要隔开的行数
let spaceList = $computed(() => {
  let selfLength = trList.filter(item => item.timetableTimeType == 1)?.length
  let morningLength = trList.filter(
    item => item.timetableTimeType == 2,
  )?.length
  let afternoonLength = trList.filter(
    item => item.timetableTimeType == 3,
  )?.length
  return [
    selfLength,
    morningLength + selfLength,
    selfLength + morningLength + afternoonLength,
  ]
})
watch(
  () => props.currentWeek,
  () => {
    if (props.currentWeek) getScheduleList()
  },
)
</script>

<template>
  <g-loading v-if="loading" class="h-[200px]"></g-loading>
  <div v-if="trList?.length && !loading" class="pb-[17px]">
    <table>
      <thead>
        <tr>
          <td>节次\周次</td>
          <td>上课时间段</td>
          <td
            v-for="(headerItem, headerIndex) in headerList"
            :key="headerIndex"
          >
            {{ headerItem }}
          </td>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="(tr, trIndex) in trList"
          :key="trIndex"
          :class="{
            'border-b-[#F3F4F9] border-b-[13px]': spaceList.includes(
              trIndex + 1,
            ),
          }"
        >
          <td class="bg-[#FAFBFF] font-500 relative w-[55px]">
            <div class="w-full h-[1px] bg-[#ebecee] absolute top-[-1px]"></div>
            {{ tr.timetableTimeName }}
            <div
              class="w-full h-[1px] bg-[#ebecee] absolute bottom-[-1px]"
            ></div>
          </td>
          <td class="bg-[#FAFBFF] font-500 relative w-[80px]">
            <div class="w-full h-[1px] bg-[#ebecee] absolute top-[-1px]"></div>
            {{ tr.beginTime.slice(0, 5) }}-{{ tr.endTime.slice(0, 5) }}
            <div
              class="w-full h-[1px] bg-[#ebecee] absolute bottom-[-1px]"
            ></div>
          </td>
          <td
            v-for="(contentItem, contentIndex) in tr.contentList"
            :key="contentIndex"
            :class="{
              'bg-[#FFF7F3]': contentItem.taskName,
            }"
            class="cursor-pointer font-500"
            @click="onCreate({ ...tr, ...contentItem, ...contentItem?.teacher })"
          >
            <div
              class="min-w-[100px] relative h-full flex items-center justify-center"
            >
              <div
                class="w-full h-[1px] bg-[#ebecee] absolute top-[-2px]"
              ></div>
              <div>
                <div v-if="contentItem?.teacher?.sysSubjectName">
                  {{ contentItem?.teacher?.sysSubjectName
                  }}-{{ contentItem?.teacher?.sysGradeName
                  }}{{ contentItem?.teacher?.className }}
                </div>
                <div
                  v-if="contentItem.task?.taskName"
                  class="flex bg-[#FF7735]/[0.1] text-[#FF7735] mt-[2px] px-[4px] br-[4px] py-[3px] max-w-[100px] items-center justify-center"
                  @click.stop="onTaskClick(contentItem?.task)"
                >
                  <div class="truncate flex-1 mr-[3px]">
                    AI课任务-{{ contentItem?.task?.taskName }}
                  </div>
                  <img
                    class="w-[11px] h-[11px]"
                    src="@/assets/img/courseSchedule/closeTask.png"
                    @click.stop="onDeleteTask(contentItem?.task)"
                  />
                </div>
                <div
                  v-if="contentItem?.existMemo"
                  class="flex justify-center items-center"
                  @click.stop="onMemoClick({ ...tr, ...contentItem })"
                >
                  <img
                    class="w-[13px] h-[13px] mr-[3px]"
                    src="@/assets/img/courseSchedule/memo.png"
                  />备忘录
                </div>
              </div>
              <div
                class="w-full h-[1px] bg-[#ebecee] absolute bottom-[-2px]"
              ></div>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <AddTypeDialog
      v-model:show="showAdd"
      :current-api-param="currentApiParam"
    ></AddTypeDialog>
    <MemoDetailDialog
      v-model:show="showMemoDetail"
      :current-week="currentWeek"
      :current-api-param="currentApiParam"
    ></MemoDetailDialog>
  </div>
  <g-empty v-if="!trList?.length && !loading"></g-empty>
</template>

<style lang="scss" scoped>
table {
  width: 100%;
  background: #fff;
}

table,
td {
  border: 1px solid #ebecee;
  text-align: center;
}

thead {
  padding: 5px 9px;
  background: #fafbff;
  color: #333;
  font-size: 11px;
  font-weight: 500;
}

tbody {
  color: #333;
  font-size: 11px;
}

tbody td {
  height: 59px;
}
</style>
